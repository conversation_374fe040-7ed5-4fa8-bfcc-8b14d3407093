import { Form } from "react-hook-form";

export default function RegisterForm() {
    return (
        <>
            <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-6">
                    {/* Hospital Selection */}
                    <FormField
                        control={registerForm.control}
                        name="hospital"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-sm font-medium text-gray-700">Hospital</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger className="w-full bg-gray-100 border-0">
                                            <SelectValue placeholder="Select hospital" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="hospital1">Bangkok Hospital</SelectItem>
                                        <SelectItem value="hospital2">Bumrungrad Hospital</SelectItem>
                                        <SelectItem value="hospital3">Siriraj Hospital</SelectItem>
                                        <SelectItem value="hospital4">Chulalongkorn Hospital</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Role Selection */}
                    <FormField
                        control={registerForm.control}
                        name="role"
                        render={({ field }) => (
                            <FormItem className="space-y-3">
                                <FormLabel className="text-sm font-medium text-gray-700">Role</FormLabel>
                                <FormControl>
                                    <RadioGroup
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                        className="flex flex-row space-x-6"
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="Leader" id="leader" />
                                            <Label htmlFor="leader" className="text-sm">Leader</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="Doctor" id="doctor" />
                                            <Label htmlFor="doctor" className="text-sm">Doctor</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="Collector" id="collector" />
                                            <Label htmlFor="collector" className="text-sm">Collector</Label>
                                        </div>
                                    </RadioGroup>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Name Fields */}
                    <div>
                        <Label className="text-sm font-medium text-gray-700 mb-3 block">First name - Last name</Label>
                        <div className="flex flex-col sm:flex-row gap-4">
                            <FormField
                                control={registerForm.control}
                                name="firstName"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormControl>
                                            <Input
                                                placeholder="Please enter your first name"
                                                className="w-full bg-gray-100 border-0"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={registerForm.control}
                                name="lastName"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormControl>
                                            <Input
                                                placeholder="Please enter your last name"
                                                className="w-full bg-gray-100 border-0"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>

                    {/* Date of Birth and Telephone */}
                    <div className="flex flex-col sm:flex-row gap-4">
                        <FormField
                            control={registerForm.control}
                            name="dateOfBirth"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormLabel className="text-sm font-medium text-gray-700">Date of Birth (B.E.)</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="date"
                                            placeholder="Select date"
                                            className="w-full bg-gray-100 border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={registerForm.control}
                            name="telephone"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormLabel className="text-sm font-medium text-gray-700">Telephone</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="tel"
                                            placeholder="Please enter your Telephone"
                                            className="w-full bg-gray-100 border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    {/* Email */}
                    <FormField
                        control={registerForm.control}
                        name="email"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-sm font-medium text-gray-700">Email</FormLabel>
                                <FormControl>
                                    <Input
                                        type="email"
                                        placeholder="Please enter your email"
                                        className="w-full bg-gray-100 border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Username */}
                    <FormField
                        control={registerForm.control}
                        name="username"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-sm font-medium text-gray-700">Username</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Please enter your username"
                                        className="w-full bg-gray-100 border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Password Fields */}
                    <div className="flex flex-col sm:flex-row gap-4">
                        <FormField
                            control={registerForm.control}
                            name="password"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormLabel className="text-sm font-medium text-gray-700">Password</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="password"
                                            placeholder="Please enter your password"
                                            className="w-full bg-gray-100 border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={registerForm.control}
                            name="confirmPassword"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormLabel className="text-sm font-medium text-gray-700">Confirm Password</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="password"
                                            placeholder="Please re-enter your password"
                                            className="w-full bg-gray-100 border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    {/* Password Requirements */}
                    <div className="text-xs text-red-500">
                        <p>The password must contain at least 1 uppercase letter, 1</p>
                        <p>lowercase letter, and 1 number, with a length of 6-12</p>
                        <p>characters.</p>
                    </div>

                    {/* Certificate Attachments */}
                    <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Certificate Attachments</Label>
                        <div className="text-sm text-gray-600">
                            Number of files uploaded: <span className="font-medium">{uploadedFiles.length} files</span>
                        </div>

                        {/* File Upload Button */}
                        <div className="flex justify-end">
                            <label htmlFor="file-upload" className="cursor-pointer">
                                <div className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">
                                    <Upload className="w-4 h-4" />
                                    Select file
                                </div>
                                <input
                                    id="file-upload"
                                    type="file"
                                    multiple
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    onChange={handleFileUpload}
                                    className="hidden"
                                />
                            </label>
                        </div>

                        {/* Uploaded Files List */}
                        {uploadedFiles.length > 0 && (
                            <div className="space-y-2">
                                {uploadedFiles.map((file) => (
                                    <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                                        <div className="flex items-center gap-2">
                                            {file.type.includes('pdf') ? (
                                                <FileText className="w-4 h-4 text-red-500" />
                                            ) : (
                                                <Image className="w-4 h-4 text-blue-500" />
                                            )}
                                            <span className="text-sm text-gray-700">{file.name}</span>
                                        </div>
                                        <button
                                            type="button"
                                            onClick={() => removeFile(file.id)}
                                            className="text-red-500 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                            <span className="text-xs ml-1">delete</span>
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3 pt-4">
                        <Button
                            type="button"
                            variant="outline"
                            className="flex-1 bg-gray-300 text-gray-700 border-0 hover:bg-gray-400"
                            onClick={() => setIsLogin(true)}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            className="flex-1"
                            style={{ backgroundColor: '#5fb3b3' }}
                        >
                            Register
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    )
}
