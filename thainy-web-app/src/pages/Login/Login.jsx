import React from "react";
import axiosInstance from "../../services/axois";

function Login() {
  const handleLogin = async () => {
    try {
      const response = await axiosInstance.get("/api/health");
      alert("API Health Success: " + JSON.stringify(response.data));
    } catch (error) {
      alert("API Health Error: " + error);
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
      <h2>Login Page</h2>
      <button style={{ padding: '10px 24px', fontSize: '16px' }} onClick={handleLogin}>Login</button>
    </div>
  );
}

export default Login;